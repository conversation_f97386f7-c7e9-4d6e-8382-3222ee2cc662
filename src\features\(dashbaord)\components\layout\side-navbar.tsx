import { NavLink } from "react-router-dom";
import { sidebarModules } from "../../../../data/sidebar-modules";

interface SideNavbarProps {
  navbar: boolean;
}

const SideNavbar: React.FC<SideNavbarProps> = ({ navbar }) => {
  return (
    <div className={`h-full`}>
      <div className="p-4 h-full bg-[#f5f5f5]">
        <nav className="space-y-2 h-full">
          {sidebarModules.map((module) => {
            const IconComponent = module.icon;
            return (
              <NavLink
                key={module.id}
                to={module.url}
                className={({ isActive }) =>
                  `flex items-center gap-6 p-3 rounded-lg transition-colors duration-200 ${
                    isActive
                      ? "bg-[#F44336] text-white"
                      : "text-[#212121] hover:bg-gray-100"
                  }`
                }
              >
                <IconComponent className="text-xl flex-shrink-0" />
                {navbar && (
                  <span className="font-medium whitespace-nowrap">
                    {module.name}
                  </span>
                )}
              </NavLink>
            );
          })}
        </nav>
      </div>
    </div>
  );
};

export default SideNavbar;
