// Navbar Theme Configuration
export const navbarTheme = {
  colors: {
    background: "#212121", // Primary orange
    text: "#68473B", // Brown text
    textHover: "#4A2F26", // Darker brown for hover
    iconColor: "#68473B", // Same as text
    iconHover: "#4A2F26", // Darker for hover
    dropdownBg: "#FFFFFF", // White dropdown background
    dropdownText: "#374151", // Gray text in dropdown
    dropdownHover: "#F9FAFB", // Light gray hover in dropdown
  },
  spacing: {
    padding: "0.5rem", // p-2
    horizontalPadding: "1.25rem", // px-5
    iconGap: "0.75rem", // gap-3
    logoGap: "2.75rem", // gap-11
  },
  transitions: {
    duration: "200ms",
    easing: "ease-in-out",
  },
  typography: {
    fontSize: {
      icon: "1.25rem", // text-[20px]
      text: "1rem",
      small: "0.875rem",
    },
    fontWeight: {
      semibold: "600",
      medium: "500",
    },
  },
} as const;

// Type for theme colors
export type NavbarThemeColors = typeof navbarTheme.colors;

// Utility function to get theme values
export const getNavbarTheme = () => navbarTheme;

// CSS-in-JS style objects (if needed)
export const navbarStyles = {
  container: {
    backgroundColor: navbarTheme.colors.background,
    padding: navbarTheme.spacing.padding,
  },
  text: {
    color: navbarTheme.colors.text,
    fontWeight: navbarTheme.typography.fontWeight.semibold,
  },
  icon: {
    color: navbarTheme.colors.iconColor,
    fontSize: navbarTheme.typography.fontSize.icon,
  },
} as const;
