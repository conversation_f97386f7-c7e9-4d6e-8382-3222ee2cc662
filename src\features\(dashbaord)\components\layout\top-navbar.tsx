import NotificationsIcon from "@mui/icons-material/Notifications";
import { FaBars } from "react-icons/fa6";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import { IoMdSettings } from "react-icons/io";
import { MdLogout } from "react-icons/md";
import { useState, useEffect, useRef } from "react";
import { useCurrentUser, useLogout } from "@/features/auth/hooks";
import logo from "../../../../assets/images/logo.png";

interface TopNavbarProps {
  handleNavbar: () => void;
  navbar: boolean;
}

const TopNavbar: React.FC<TopNavbarProps> = ({ handleNavbar }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const currentUser = useCurrentUser();
  const logoutMutation = useLogout();

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  return (
    <div className=" w-full bg-[#f5f5f5] p-2">
      <div className="flex items-center justify-between px-5">
        <div className="flex items-center justify-center gap-11">
          <div
            onClick={handleNavbar}
            className="text-[20px] flex justify-center text-navbar cursor-pointer"
          >
            <FaBars />
          </div>
          <div className=" flex justify-center">
            <img className="h-[50px] w-[50px]" src={logo} alt="Logo" />
          </div>
        </div>

        <div className="flex justify-center text-navbar gap-3 items-center relative">
          <IoMdSettings className="text-lg cursor-pointer" />
          <NotificationsIcon className="cursor-pointer" />

          {/* User Profile Dropdown */}
          <div className="relative" ref={dropdownRef}>
            <div
              className="flex gap-2 items-center cursor-pointer"
              onClick={() => setShowDropdown(!showDropdown)}
            >
              <p className="text-navbar font-semibold">
                {currentUser?.name || currentUser?.email || "User"}
              </p>
              <ArrowDropDownIcon />
            </div>

            {/* Dropdown Menu */}
            {showDropdown && (
              <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                <div className="py-2">
                  <div className="px-4 py-2 border-b border-gray-100">
                    <p className="text-sm font-medium text-gray-900">
                      {currentUser?.name || "User"}
                    </p>
                    <p className="text-sm text-gray-500">
                      {currentUser?.email}
                    </p>
                  </div>
                  <button
                    onClick={handleLogout}
                    disabled={logoutMutation.isPending}
                    className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2 disabled:opacity-50"
                  >
                    <MdLogout className="text-base" />
                    {logoutMutation.isPending ? "Signing out..." : "Sign out"}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopNavbar;
